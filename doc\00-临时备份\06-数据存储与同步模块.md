# 06-数据存储与同步模块

## 模块概述

### 模块名称
数据存储与同步模块 (DataManager)

### 功能定位
作为系统的数据层模块，负责数据持久化、离线存储、云端同步、数据安全保护，为所有功能模块提供可靠的数据服务。

### 设计目标
- 提供可靠的数据持久化服务
- 支持离线使用和在线同步
- 确保数据安全和隐私保护
- 实现多设备数据一致性
- 提供高效的数据访问接口

### 核心价值
- 数据安全可靠
- 离线功能完善
- 同步机制智能
- 性能表现优异

## 存储架构设计

### 分层存储架构
```
┌─────────────────────────────────────────┐
│              应用层                      │
├─────────────────────────────────────────┤
│              数据访问层                  │
├─────────────────────────────────────────┤
│              数据同步层                  │
├─────────────────────────────────────────┤
│              本地存储层                  │
├─────────────────────────────────────────┤
│              云端存储层                  │
└─────────────────────────────────────────┘
```

### 存储方案设计

#### 1. 本地存储
- **LocalStorage**
  - 用户偏好设置
  - 界面配置信息
  - 临时缓存数据
  - 容量限制：5-10MB

- **IndexedDB**
  - 任务数据存储
  - 思维导图数据
  - 学习记录数据
  - 离线队列数据
  - 容量限制：50MB-1GB

- **WebSQL（备用）**
  - 兼容性考虑
  - 结构化数据存储
  - 复杂查询支持

#### 2. 云端存储
- **腾讯云 MongoDB**
  - 主要数据存储
  - 文档型数据库
  - 灵活的数据结构
  - 高可用性保证

- **腾讯云 MySQL**
  - 关系型数据存储
  - 用户账户信息
  - 系统配置数据
  - 事务支持

- **腾讯云 COS**
  - 文件存储服务
  - 图片、音频文件
  - 附件和资源
  - CDN加速

## 数据模型设计

### 1. 核心数据实体

#### 用户数据模型
```javascript
User = {
  id: String,              // 用户唯一标识
  username: String,        // 用户名
  email: String,           // 邮箱
  profile: {
    nickname: String,      // 昵称
    avatar: String,        // 头像URL
    grade: String,         // 年级
    school: String         // 学校
  },
  settings: {
    theme: String,         // 主题设置
    language: String,      // 语言设置
    notifications: Object, // 通知设置
    privacy: Object        // 隐私设置
  },
  createdAt: Date,
  updatedAt: Date,
  lastLoginAt: Date
}
```

#### 任务数据模型
```javascript
Task = {
  id: String,              // 任务ID
  userId: String,          // 用户ID
  title: String,           // 任务标题
  content: String,         // 任务内容
  subject: String,         // 学科分类
  estimatedTime: Number,   // 预估时间
  actualTime: Number,      // 实际时间
  status: String,          // 任务状态
  priority: Number,        // 优先级
  difficulty: Number,      // 难度级别
  source: String,          // 来源类型
  sourceId: String,        // 来源ID
  reviewSchedule: Array,   // 复习计划
  tags: Array,            // 标签
  attachments: Array,     // 附件
  createdAt: Date,
  updatedAt: Date,
  syncStatus: String,     // 同步状态
  version: Number         // 版本号
}
```

#### 思维导图数据模型
```javascript
MindMap = {
  id: String,              // 导图ID
  userId: String,          // 用户ID
  title: String,           // 导图标题
  subject: String,         // 学科分类
  description: String,     // 描述
  nodes: Array,           // 节点数据
  connections: Array,     // 连接关系
  style: Object,          // 样式设置
  metadata: {
    nodeCount: Number,    // 节点数量
    lastEditedNode: String, // 最后编辑节点
    version: String       // 版本信息
  },
  createdAt: Date,
  updatedAt: Date,
  syncStatus: String,
  version: Number
}
```

### 2. 数据关系设计

#### 实体关系图
```
User (1) ──── (N) Task
User (1) ──── (N) MindMap
User (1) ──── (N) LearningRecord
Task (1) ──── (N) ReviewRecord
MindMap (1) ──── (N) MindMapNode
MindMapNode (N) ──── (N) Task
```

#### 数据完整性约束
- 外键约束：确保数据关联完整性
- 唯一性约束：防止重复数据
- 非空约束：确保必要字段有值
- 检查约束：验证数据有效性

## 数据访问层设计

### 1. 数据访问接口

#### 基础CRUD接口
```javascript
// 数据访问基类
class BaseRepository {
  async create(data) {
    // 创建数据
    const result = await this.localDB.create(data);
    await this.syncQueue.add('create', result);
    return result;
  }

  async findById(id) {
    // 优先从本地获取
    let result = await this.localDB.findById(id);
    if (!result && this.isOnline()) {
      result = await this.remoteDB.findById(id);
      if (result) {
        await this.localDB.create(result);
      }
    }
    return result;
  }

  async update(id, data) {
    // 更新数据
    const result = await this.localDB.update(id, data);
    await this.syncQueue.add('update', result);
    return result;
  }

  async delete(id) {
    // 软删除
    const result = await this.localDB.update(id, { 
      deleted: true, 
      deletedAt: new Date() 
    });
    await this.syncQueue.add('delete', { id });
    return result;
  }

  async findAll(query = {}) {
    // 查询数据
    return await this.localDB.findAll(query);
  }
}
```

#### 专用数据访问类
```javascript
// 任务数据访问
class TaskRepository extends BaseRepository {
  async findBySubject(subject) {
    return await this.findAll({ subject, deleted: { $ne: true } });
  }

  async findByStatus(status) {
    return await this.findAll({ status, deleted: { $ne: true } });
  }

  async findByDateRange(startDate, endDate) {
    return await this.findAll({
      createdAt: { $gte: startDate, $lte: endDate },
      deleted: { $ne: true }
    });
  }
}

// 思维导图数据访问
class MindMapRepository extends BaseRepository {
  async findWithNodes(id) {
    const mindMap = await this.findById(id);
    if (mindMap) {
      mindMap.nodes = await this.nodeRepository.findByMapId(id);
    }
    return mindMap;
  }

  async updateNode(mapId, nodeId, nodeData) {
    await this.nodeRepository.update(nodeId, nodeData);
    await this.update(mapId, { updatedAt: new Date() });
  }
}
```

### 2. 数据缓存策略

#### 多级缓存架构
```javascript
// 缓存管理器
class CacheManager {
  constructor() {
    this.memoryCache = new Map();     // 内存缓存
    this.localCache = new LocalDB();  // 本地缓存
    this.remoteCache = new RemoteDB(); // 远程缓存
  }

  async get(key) {
    // 1. 检查内存缓存
    if (this.memoryCache.has(key)) {
      return this.memoryCache.get(key);
    }

    // 2. 检查本地缓存
    let data = await this.localCache.get(key);
    if (data) {
      this.memoryCache.set(key, data);
      return data;
    }

    // 3. 从远程获取
    if (this.isOnline()) {
      data = await this.remoteCache.get(key);
      if (data) {
        await this.localCache.set(key, data);
        this.memoryCache.set(key, data);
      }
    }

    return data;
  }

  async set(key, value) {
    // 同时更新所有缓存层
    this.memoryCache.set(key, value);
    await this.localCache.set(key, value);
    if (this.isOnline()) {
      await this.remoteCache.set(key, value);
    }
  }

  async invalidate(key) {
    // 清除所有缓存层
    this.memoryCache.delete(key);
    await this.localCache.delete(key);
    if (this.isOnline()) {
      await this.remoteCache.delete(key);
    }
  }
}
```

#### 缓存策略配置
```javascript
const cacheConfig = {
  // 内存缓存配置
  memory: {
    maxSize: 100,           // 最大缓存项数
    ttl: 5 * 60 * 1000,    // 5分钟过期
    checkPeriod: 60 * 1000  // 1分钟检查一次
  },

  // 本地缓存配置
  local: {
    maxSize: 50 * 1024 * 1024, // 50MB
    ttl: 24 * 60 * 60 * 1000,  // 24小时过期
    compression: true           // 启用压缩
  },

  // 缓存策略
  strategies: {
    tasks: 'write-through',     // 写透策略
    mindmaps: 'write-back',     // 写回策略
    settings: 'write-around'    // 写绕过策略
  }
};
```

## 数据同步机制

### 1. 同步策略设计

#### 增量同步算法
```javascript
class IncrementalSync {
  async syncData() {
    const lastSyncTime = await this.getLastSyncTime();
    
    // 1. 获取本地变更
    const localChanges = await this.getLocalChanges(lastSyncTime);
    
    // 2. 获取远程变更
    const remoteChanges = await this.getRemoteChanges(lastSyncTime);
    
    // 3. 冲突检测和解决
    const conflicts = this.detectConflicts(localChanges, remoteChanges);
    const resolvedChanges = await this.resolveConflicts(conflicts);
    
    // 4. 应用变更
    await this.applyLocalChanges(remoteChanges);
    await this.applyRemoteChanges(localChanges);
    
    // 5. 更新同步时间戳
    await this.updateLastSyncTime(new Date());
    
    return {
      localChanges: localChanges.length,
      remoteChanges: remoteChanges.length,
      conflicts: conflicts.length,
      resolved: resolvedChanges.length
    };
  }

  detectConflicts(localChanges, remoteChanges) {
    const conflicts = [];
    
    localChanges.forEach(local => {
      const remote = remoteChanges.find(r => r.id === local.id);
      if (remote && remote.version !== local.version) {
        conflicts.push({ local, remote });
      }
    });
    
    return conflicts;
  }

  async resolveConflicts(conflicts) {
    const resolved = [];
    
    for (const conflict of conflicts) {
      const resolution = await this.conflictResolver.resolve(conflict);
      resolved.push(resolution);
    }
    
    return resolved;
  }
}
```

#### 冲突解决策略
```javascript
class ConflictResolver {
  async resolve(conflict) {
    const { local, remote } = conflict;
    
    // 1. 时间戳优先策略
    if (local.updatedAt > remote.updatedAt) {
      return this.useLocal(conflict);
    } else if (remote.updatedAt > local.updatedAt) {
      return this.useRemote(conflict);
    }
    
    // 2. 用户选择策略
    if (this.requiresUserInput(conflict)) {
      return await this.promptUser(conflict);
    }
    
    // 3. 合并策略
    if (this.canMerge(conflict)) {
      return this.mergeChanges(conflict);
    }
    
    // 4. 默认策略：保留远程版本
    return this.useRemote(conflict);
  }

  canMerge(conflict) {
    // 检查是否可以自动合并
    const { local, remote } = conflict;
    
    // 对于任务数据，检查是否只是不同字段的修改
    const localFields = Object.keys(local.changes || {});
    const remoteFields = Object.keys(remote.changes || {});
    
    return localFields.every(field => !remoteFields.includes(field));
  }

  mergeChanges(conflict) {
    const { local, remote } = conflict;
    
    return {
      ...remote,
      ...local.changes,
      ...remote.changes,
      version: Math.max(local.version, remote.version) + 1,
      mergedAt: new Date()
    };
  }
}
```

### 2. 离线队列管理

#### 离线操作队列
```javascript
class OfflineQueue {
  constructor() {
    this.queue = [];
    this.processing = false;
  }

  async add(operation, data) {
    const queueItem = {
      id: this.generateId(),
      operation,
      data,
      timestamp: new Date(),
      retryCount: 0,
      status: 'pending'
    };
    
    this.queue.push(queueItem);
    await this.saveQueue();
    
    // 如果在线，立即尝试处理
    if (this.isOnline() && !this.processing) {
      this.processQueue();
    }
  }

  async processQueue() {
    if (this.processing || this.queue.length === 0) {
      return;
    }
    
    this.processing = true;
    
    while (this.queue.length > 0 && this.isOnline()) {
      const item = this.queue[0];
      
      try {
        await this.processItem(item);
        this.queue.shift();
        item.status = 'completed';
      } catch (error) {
        item.retryCount++;
        item.lastError = error.message;
        
        if (item.retryCount >= 3) {
          this.queue.shift();
          item.status = 'failed';
          await this.handleFailedItem(item);
        } else {
          // 指数退避重试
          await this.delay(Math.pow(2, item.retryCount) * 1000);
        }
      }
    }
    
    await this.saveQueue();
    this.processing = false;
  }

  async processItem(item) {
    switch (item.operation) {
      case 'create':
        return await this.remoteDB.create(item.data);
      case 'update':
        return await this.remoteDB.update(item.data.id, item.data);
      case 'delete':
        return await this.remoteDB.delete(item.data.id);
      default:
        throw new Error(`Unknown operation: ${item.operation}`);
    }
  }
}
```

### 3. 实时同步机制

#### WebSocket连接管理
```javascript
class RealtimeSync {
  constructor() {
    this.ws = null;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 1000;
  }

  connect() {
    this.ws = new WebSocket(this.getWebSocketUrl());
    
    this.ws.onopen = () => {
      console.log('WebSocket connected');
      this.reconnectAttempts = 0;
      this.authenticate();
    };
    
    this.ws.onmessage = (event) => {
      const message = JSON.parse(event.data);
      this.handleMessage(message);
    };
    
    this.ws.onclose = () => {
      console.log('WebSocket disconnected');
      this.scheduleReconnect();
    };
    
    this.ws.onerror = (error) => {
      console.error('WebSocket error:', error);
    };
  }

  handleMessage(message) {
    switch (message.type) {
      case 'data_changed':
        this.handleDataChange(message.data);
        break;
      case 'sync_request':
        this.handleSyncRequest(message.data);
        break;
      case 'conflict_detected':
        this.handleConflict(message.data);
        break;
    }
  }

  async handleDataChange(data) {
    // 检查是否是当前用户的数据
    if (data.userId === this.currentUserId) {
      // 更新本地数据
      await this.updateLocalData(data);
      
      // 通知应用层数据变更
      this.eventBus.emit('data_changed', data);
    }
  }
}
```

## 数据安全设计

### 1. 数据加密

#### 客户端加密
```javascript
class DataEncryption {
  constructor() {
    this.algorithm = 'AES-GCM';
    this.keyLength = 256;
  }

  async generateKey() {
    return await crypto.subtle.generateKey(
      {
        name: this.algorithm,
        length: this.keyLength
      },
      true,
      ['encrypt', 'decrypt']
    );
  }

  async encrypt(data, key) {
    const iv = crypto.getRandomValues(new Uint8Array(12));
    const encodedData = new TextEncoder().encode(JSON.stringify(data));
    
    const encryptedData = await crypto.subtle.encrypt(
      {
        name: this.algorithm,
        iv: iv
      },
      key,
      encodedData
    );
    
    return {
      data: Array.from(new Uint8Array(encryptedData)),
      iv: Array.from(iv)
    };
  }

  async decrypt(encryptedData, key) {
    const decryptedData = await crypto.subtle.decrypt(
      {
        name: this.algorithm,
        iv: new Uint8Array(encryptedData.iv)
      },
      key,
      new Uint8Array(encryptedData.data)
    );
    
    const decodedData = new TextDecoder().decode(decryptedData);
    return JSON.parse(decodedData);
  }
}
```

#### 敏感数据处理
```javascript
class SensitiveDataManager {
  constructor() {
    this.encryptionKey = null;
    this.sensitiveFields = [
      'email', 'phone', 'realName', 'idCard'
    ];
  }

  async initializeKey() {
    // 从用户密码派生加密密钥
    const password = await this.getUserPassword();
    const salt = await this.getSalt();
    
    this.encryptionKey = await crypto.subtle.importKey(
      'raw',
      await crypto.subtle.deriveBits(
        {
          name: 'PBKDF2',
          salt: salt,
          iterations: 100000,
          hash: 'SHA-256'
        },
        await crypto.subtle.importKey(
          'raw',
          new TextEncoder().encode(password),
          'PBKDF2',
          false,
          ['deriveBits']
        ),
        256
      ),
      { name: 'AES-GCM' },
      false,
      ['encrypt', 'decrypt']
    );
  }

  async encryptSensitiveData(data) {
    const result = { ...data };
    
    for (const field of this.sensitiveFields) {
      if (result[field]) {
        result[field] = await this.encryption.encrypt(
          result[field], 
          this.encryptionKey
        );
      }
    }
    
    return result;
  }
}
```

### 2. 访问控制

#### 权限验证
```javascript
class AccessControl {
  constructor() {
    this.permissions = new Map();
  }

  async checkPermission(userId, resource, action) {
    const userPermissions = await this.getUserPermissions(userId);
    const resourcePermissions = userPermissions[resource] || [];
    
    return resourcePermissions.includes(action) || 
           resourcePermissions.includes('*');
  }

  async getUserPermissions(userId) {
    if (!this.permissions.has(userId)) {
      const permissions = await this.loadUserPermissions(userId);
      this.permissions.set(userId, permissions);
    }
    
    return this.permissions.get(userId);
  }

  async validateDataAccess(userId, dataType, dataId) {
    // 检查用户是否有权访问特定数据
    const data = await this.dataRepository.findById(dataId);
    
    if (!data) {
      throw new Error('Data not found');
    }
    
    if (data.userId !== userId) {
      const hasPermission = await this.checkPermission(
        userId, 
        dataType, 
        'read_others'
      );
      
      if (!hasPermission) {
        throw new Error('Access denied');
      }
    }
    
    return true;
  }
}
```

## 性能优化

### 1. 数据库优化

#### 索引策略
```javascript
const indexConfig = {
  tasks: [
    { userId: 1, status: 1 },
    { userId: 1, subject: 1 },
    { userId: 1, createdAt: -1 },
    { userId: 1, updatedAt: -1 }
  ],
  mindmaps: [
    { userId: 1, subject: 1 },
    { userId: 1, updatedAt: -1 }
  ],
  users: [
    { email: 1 },
    { username: 1 }
  ]
};
```

#### 查询优化
```javascript
class QueryOptimizer {
  async optimizeQuery(query) {
    // 1. 添加必要的索引字段
    if (!query.userId && this.currentUserId) {
      query.userId = this.currentUserId;
    }
    
    // 2. 限制返回字段
    const projection = this.getProjection(query);
    
    // 3. 添加分页
    const pagination = this.getPagination(query);
    
    // 4. 优化排序
    const sort = this.optimizeSort(query.sort);
    
    return {
      filter: query,
      projection,
      sort,
      ...pagination
    };
  }

  getProjection(query) {
    // 根据查询类型返回必要字段
    const baseFields = ['id', 'title', 'status', 'updatedAt'];
    
    if (query.detailed) {
      return null; // 返回所有字段
    }
    
    return baseFields.reduce((proj, field) => {
      proj[field] = 1;
      return proj;
    }, {});
  }
}
```

### 2. 缓存优化

#### 智能预加载
```javascript
class PreloadManager {
  constructor() {
    this.preloadRules = new Map();
  }

  addRule(trigger, preloadTargets) {
    this.preloadRules.set(trigger, preloadTargets);
  }

  async onDataAccess(dataType, dataId) {
    const rules = this.preloadRules.get(dataType);
    
    if (rules) {
      for (const rule of rules) {
        this.schedulePreload(rule, dataId);
      }
    }
  }

  async schedulePreload(rule, contextId) {
    // 延迟预加载，避免阻塞当前操作
    setTimeout(async () => {
      try {
        await this.executePreload(rule, contextId);
      } catch (error) {
        console.warn('Preload failed:', error);
      }
    }, rule.delay || 100);
  }
}
```

---

**文档版本**：v2.0 重构版
**创建时间**：2025-01-31
**模块特点**：数据安全可靠、离线功能完善、同步机制智能
**依赖关系**：为所有功能模块提供数据服务，无外部模块依赖
