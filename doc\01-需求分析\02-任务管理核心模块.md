# 02-任务管理核心模块

## 模块概述

### 模块名称
任务管理核心模块 (TaskManager)

### 功能定位
系统的核心基础模块，负责学习任务的完整生命周期管理，实现艾宾浩斯记忆曲线算法，提供任务CRUD操作和智能提醒机制。

### 设计目标
- 实现科学的艾宾浩斯记忆曲线算法
- 提供完整的任务管理功能
- 支持多种内容格式和学科分类
- 提供可靠的提醒机制
- 作为其他模块的基础服务

### 模块特性
- **独立性**：不依赖其他业务模块，作为系统基础服务
- **稳定性**：核心算法经过验证，确保数据准确性
- **扩展性**：提供标准接口，支持其他模块调用
- **可靠性**：完善的错误处理和数据保护机制

## 核心功能设计

### 1. 任务CRUD操作

#### 1.1 任务创建
- **支持内容格式**
  - 文本内容：支持富文本编辑
  - 图片内容：支持图片上传和预览
  - 语音内容：支持语音录入和播放
  - 混合内容：支持多种格式组合

- **学科分类支持**
  - 数学、语文、英语（主要学科）
  - 物理、化学、生物（理科）
  - 历史、地理、政治（文科）
  - 支持自定义学科扩展

- **任务属性设置**
  - 任务标题（必填，1-100字符）
  - 任务内容（必填，1-5000字符）
  - 学科分类（必选）
  - 预估时间（1-300分钟）
  - 优先级（1-5级）
  - 难度级别（1-5级）
  - 标签和备注

#### 1.2 任务查询
- **列表查询**
  - 按学科筛选
  - 按状态筛选（待处理、进行中、已完成）
  - 按时间范围筛选
  - 按优先级排序
  - 支持分页和搜索

- **详情查询**
  - 完整任务信息
  - 复习计划详情
  - 学习历史记录
  - 关联数据统计

#### 1.3 任务更新
- **内容修改**
  - 支持任务标题和内容修改
  - 支持属性调整（优先级、难度等）
  - 支持标签和备注更新
  - 修改历史记录

- **状态管理**
  - 任务状态转换（待处理→进行中→已完成）
  - 学习进度更新
  - 复习状态记录
  - 异常状态处理

#### 1.4 任务删除
- **安全删除**
  - 删除确认机制
  - 关联数据清理
  - 删除日志记录
  - 支持恢复功能（软删除）

### 2. 艾宾浩斯记忆曲线算法

#### 2.1 标准时间间隔
```
复习时间节点（9个节点）：
1. 5分钟后
2. 30分钟后
3. 12小时后
4. 1天后
5. 3天后
6. 1周后
7. 2周后
8. 1个月后
9. 2个月后
```

#### 2.2 复习计划生成
- **自动计算**
  - 基于任务创建时间计算复习时间点
  - 考虑用户学习时间偏好
  - 避开休息日和特殊日期
  - 支持手动调整复习时间

- **计划优化**
  - 检测时间冲突
  - 负载均衡考虑
  - 学习效率优化
  - 个性化调整

#### 2.3 复习进度跟踪
- **进度记录**
  - 每个复习节点的完成状态
  - 实际复习时间记录
  - 复习质量评估
  - 遗忘曲线分析

- **自适应调整**
  - 根据复习效果调整间隔
  - 基于个人表现优化算法
  - 支持复习计划重置
  - 异常情况处理

### 3. 智能提醒机制

#### 3.1 浏览器通知
- **通知类型**
  - 复习时间到达提醒
  - 任务截止日期提醒
  - 学习计划提醒
  - 重要任务提醒

- **通知设置**
  - 提醒开关控制
  - 提醒时间设置
  - 提醒频率调整
  - 免打扰时间段

#### 3.2 消息中心
- **消息管理**
  - 未读消息统计
  - 消息分类显示
  - 消息历史记录
  - 批量操作支持

- **消息类型**
  - 系统通知
  - 学习提醒
  - 进度报告
  - 异常警告

#### 3.3 智能提醒策略
- **提醒时机**
  - 提前15分钟预提醒
  - 准时提醒
  - 延迟提醒（未响应时）
  - 重要任务多次提醒

- **提醒内容**
  - 任务标题和内容摘要
  - 预估学习时间
  - 当前学习负载
  - 操作快捷入口

## 数据模型设计

### 任务数据模型
```javascript
Task = {
  // 基础信息
  id: String,              // 任务唯一标识 (UUID)
  title: String,           // 任务标题 (必填, 1-100字符)
  content: String,         // 任务内容 (必填, 1-5000字符)
  subject: String,         // 学科分类
  
  // 时间信息
  estimatedTime: Number,   // 预估时间(分钟, 1-300)
  actualTime: Number,      // 实际时间(分钟, 0-600)
  createdAt: Date,         // 创建时间
  scheduledAt: Date,       // 计划执行时间
  completedAt: Date,       // 完成时间
  
  // 状态信息
  status: String,          // 状态: pending|active|completed|cancelled
  priority: Number,        // 优先级 (1-5, 5最高)
  difficulty: Number,      // 难度级别 (1-5, 5最难)
  
  // 来源信息
  source: String,          // 来源: manual|mindmap|import
  sourceId: String,        // 来源关联ID
  
  // 记忆曲线信息
  reviewSchedule: Array,   // 复习计划
  currentReviewIndex: Number, // 当前复习节点索引
  
  // 扩展信息
  tags: Array,            // 标签列表
  notes: String,          // 备注信息
  attachments: Array      // 附件列表
}
```

### 复习计划模型
```javascript
ReviewSchedule = {
  taskId: String,         // 关联任务ID
  reviewIndex: Number,    // 复习节点索引 (0-8)
  scheduledDate: Date,    // 计划复习时间
  actualDate: Date,       // 实际复习时间
  completed: Boolean,     // 是否已完成
  quality: Number,        // 复习质量评分 (1-5)
  duration: Number,       // 复习用时 (分钟)
  notes: String          // 复习备注
}
```

## 接口设计

### API接口规范

#### 任务管理接口
```javascript
// 创建任务
POST /api/v1/task/tasks
{
  title: String,
  content: String,
  subject: String,
  estimatedTime: Number,
  priority: Number,
  difficulty: Number,
  tags: Array,
  notes: String
}

// 获取任务列表
GET /api/v1/task/tasks?subject={subject}&status={status}&page={page}&limit={limit}

// 获取任务详情
GET /api/v1/task/tasks/{id}

// 更新任务
PUT /api/v1/task/tasks/{id}
{
  // 需要更新的字段
}

// 删除任务
DELETE /api/v1/task/tasks/{id}
```

#### 复习计划接口
```javascript
// 获取复习计划
GET /api/v1/task/schedule/{date}

// 更新复习进度
PUT /api/v1/task/schedule/{taskId}
{
  reviewIndex: Number,
  completed: Boolean,
  quality: Number,
  duration: Number,
  notes: String
}

// 批量创建复习计划
POST /api/v1/task/schedule/batch
{
  tasks: Array // 任务ID列表
}
```

### 事件接口

#### 发出的事件
- **TASK_CREATED**：任务创建事件
- **TASK_UPDATED**：任务更新事件
- **TASK_COMPLETED**：任务完成事件
- **TASK_DELETED**：任务删除事件
- **REVIEW_SCHEDULED**：复习计划生成事件
- **REVIEW_COMPLETED**：复习完成事件

#### 事件数据格式
```javascript
TaskEvent = {
  type: String,           // 事件类型
  source: 'TaskManager',  // 事件来源
  data: {
    taskId: String,       // 任务ID
    task: Object,         // 任务对象（创建时）
    changes: Object,      // 变更内容（更新时）
    timestamp: Date       // 事件时间
  }
}
```

## 业务逻辑

### 任务生命周期
```
创建 → 待处理 → 进行中 → 已完成
  ↓      ↓        ↓        ↓
 删除   取消     暂停     归档
```

### 复习流程
```
任务创建 → 生成复习计划 → 提醒复习 → 执行复习 → 记录结果 → 下一轮复习
```

### 数据验证规则
- 任务标题不能为空，长度1-100字符
- 任务内容不能为空，长度1-5000字符
- 预估时间范围1-300分钟
- 优先级和难度级别范围1-5
- 学科分类必须在预定义列表中
- 复习时间不能早于创建时间

## 性能优化

### 数据库优化
- 任务ID建立主键索引
- 学科和状态建立复合索引
- 创建时间建立时间索引
- 复习计划按日期分区

### 缓存策略
- 热点任务数据缓存
- 复习计划缓存
- 用户偏好设置缓存
- 学科配置缓存

### 批量操作
- 支持批量创建任务
- 支持批量更新状态
- 支持批量生成复习计划
- 支持批量删除操作

## 错误处理

### 异常类型
- 数据验证异常
- 数据库操作异常
- 业务逻辑异常
- 系统运行异常

### 处理策略
- 输入验证和错误提示
- 数据库事务回滚
- 异常日志记录
- 用户友好的错误信息

### 容错机制
- 数据备份和恢复
- 服务降级策略
- 重试机制
- 故障转移

---

**文档版本**：v2.0 重构版
**创建时间**：2025-01-31
**模块特点**：核心基础服务、算法科学可靠、接口标准规范
**依赖关系**：无外部模块依赖，为其他模块提供基础服务
