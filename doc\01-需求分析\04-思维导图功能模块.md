# 04-思维导图功能模块

## 模块概述

### 模块名称
思维导图功能模块 (MindMapManager)

### 功能定位
提供图形化的知识管理功能，支持思维导图创建、编辑和与学习任务的深度关联，帮助用户构建结构化的知识体系，并与艾宾浩斯记忆曲线系统无缝集成。

### 设计目标
- 可视化知识结构，提升学习理解效果
- 与艾宾浩斯记忆曲线系统无缝集成
- 支持从思维导图直接创建学习任务
- 提供直观的学习进度展示
- 简化操作流程，适合初中生使用

### 核心价值
- 结构化思维培养
- 知识关联性展示
- 学习路径可视化
- 任务管理集成化

## 核心功能设计

### 1. 思维导图创建与编辑

#### 1.1 节点管理功能
- **节点创建**
  - 支持文本节点、图片节点、链接节点
  - 多级节点层次结构（支持无限层级）
  - 节点内容支持富文本编辑
  - 节点颜色和图标自定义

- **节点编辑**
  - 双击编辑节点内容
  - 拖拽调整节点位置
  - 节点大小自适应内容
  - 支持节点复制、粘贴、删除

- **节点属性**
  - 节点类型：概念、定义、公式、例题、总结
  - 重要程度：高、中、低三级标识
  - 学习状态：未学习、学习中、已掌握
  - 关联任务：绑定的学习任务列表

#### 1.2 连接线管理
- **连接创建**
  - 拖拽方式创建节点间连接
  - 支持有向连接和无向连接
  - 连接线样式自定义（颜色、粗细、虚实）
  - 连接标签添加关系说明

- **关系类型**
  - 包含关系：父子概念
  - 并列关系：同级概念
  - 因果关系：逻辑推导
  - 对比关系：异同比较

#### 1.3 样式自定义
- **主题模板**
  - 学科主题：数学、语文、英语等
  - 色彩方案：清新、商务、护眼等
  - 布局样式：树形、放射、网络等

- **个性化设置**
  - 字体大小和样式
  - 节点形状和边框
  - 背景颜色和纹理
  - 导图整体风格

### 2. 知识结构可视化

#### 2.1 学科知识点关系图
- **知识点层次**
  - 学科 → 章节 → 知识点 → 细分概念
  - 清晰的层级结构展示
  - 知识点间的逻辑关系可视化
  - 支持知识点搜索和定位

- **关系网络**
  - 前置知识依赖关系
  - 知识点间的关联性
  - 跨章节知识点连接
  - 重点难点标识

#### 2.2 学习进度可视化
- **进度标识**
  - 节点颜色表示掌握程度
  - 进度条显示整体完成度
  - 时间轴展示学习历程
  - 成就徽章激励机制

- **统计分析**
  - 各学科学习进度对比
  - 知识点掌握情况统计
  - 学习时间分布分析
  - 复习频率热力图

#### 2.3 复习路径展示
- **智能路径规划**
  - 基于知识点依赖关系规划学习顺序
  - 结合艾宾浩斯曲线安排复习路径
  - 个性化推荐学习重点
  - 薄弱环节优先提醒

- **路径可视化**
  - 学习路径高亮显示
  - 当前学习位置标识
  - 下一步学习建议
  - 复习计划时间线

### 3. 任务关联与集成

#### 3.1 从思维导图创建学习任务

- **多节点选择机制**
  - 支持单击选择单个节点
  - 支持Ctrl+单击选择多个节点
  - 支持框选批量选择相邻节点
  - 选中节点高亮显示，显示选择数量

- **右键菜单功能**
  ```
  右键菜单选项：
  ├── 创建学习任务
  │   ├── 单个任务创建
  │   ├── 批量任务创建
  │   └── 智能任务规划
  ├── 节点操作
  │   ├── 编辑节点
  │   ├── 复制节点
  │   ├── 删除节点
  │   └── 设置重要程度
  ├── 学习管理
  │   ├── 查看关联任务
  │   ├── 标记学习状态
  │   └── 添加学习笔记
  └── 高级功能
      ├── 导出为任务模板
      ├── 设置学习路径
      └── 生成复习计划
  ```

- **智能任务创建流程**
  - 选择节点 → 右键菜单 → "创建学习任务"
  - 系统分析节点内容和重要程度
  - 调用时间管理模块计算预估时间
  - 检查当前学习负载状况
  - 提供任务创建确认界面
  - 显示负载影响和调度建议

- **批量任务创建增强**
  - 多节点选择后右键选择"批量任务创建"
  - 智能分析节点间的依赖关系
  - 自动排序学习顺序（前置知识优先）
  - 计算总体时间负荷和分布
  - 提供负载均衡优化建议
  - 支持用户手动调整任务安排

#### 3.2 任务状态同步显示
- **实时状态更新**
  - 任务完成状态在导图中实时显示
  - 复习进度通过节点颜色反映
  - 任务超期提醒在导图中标识
  - 学习成果在导图中可视化

- **双向关联**
  - 导图节点变更自动更新关联任务
  - 任务内容修改同步到导图节点
  - 任务删除时导图节点状态重置
  - 保持数据一致性

#### 3.3 学习数据集成
- **学习记录关联**
  - 每个节点关联的学习时间统计
  - 复习次数和效果记录
  - 错误率和改进建议
  - 学习笔记和心得

- **智能分析**
  - 基于学习数据优化导图结构
  - 识别学习瓶颈和难点
  - 推荐相关知识点学习
  - 生成个性化学习报告

## 数据模型设计

### 思维导图数据模型
```javascript
MindMap = {
  id: String,               // 导图ID
  title: String,            // 导图标题
  subject: String,          // 学科分类
  description: String,      // 导图描述
  nodes: Array,             // 节点列表
  connections: Array,       // 连接关系
  style: Object,            // 样式设置
  metadata: {
    createdAt: Date,
    updatedAt: Date,
    version: String,
    author: String
  }
}
```

### 节点数据模型
```javascript
MindMapNode = {
  id: String,               // 节点ID
  mapId: String,            // 所属导图ID
  parentId: String,         // 父节点ID
  title: String,            // 节点标题
  content: String,          // 节点内容
  type: String,             // 节点类型
  importance: Number,       // 重要程度 1-5
  
  // 位置和样式
  position: {x: Number, y: Number},
  style: {
    color: String,          // 节点颜色
    shape: String,          // 节点形状
    fontSize: Number        // 字体大小
  },
  
  // 学习相关
  taskIds: Array,           // 关联任务ID列表
  learningStatus: String,   // 学习状态
  masteryLevel: Number,     // 掌握程度 0-100
  studyTime: Number,        // 累计学习时间
  reviewCount: Number,      // 复习次数
  
  // 时间信息
  createdAt: Date,
  updatedAt: Date
}
```

### 连接关系模型
```javascript
Connection = {
  id: String,               // 连接ID
  mapId: String,            // 所属导图ID
  sourceNodeId: String,     // 源节点ID
  targetNodeId: String,     // 目标节点ID
  type: String,             // 关系类型
  label: String,            // 连接标签
  style: {
    color: String,          // 连接线颜色
    width: Number,          // 连接线宽度
    style: String           // 连接线样式
  }
}
```

## 接口设计

### API接口

#### 思维导图管理接口
```javascript
// 创建思维导图
POST /api/v1/mindmap/maps
{
  title: String,
  subject: String,
  description: String,
  style: Object
}

// 获取思维导图
GET /api/v1/mindmap/maps/{id}

// 更新思维导图
PUT /api/v1/mindmap/maps/{id}

// 删除思维导图
DELETE /api/v1/mindmap/maps/{id}

// 获取用户的思维导图列表
GET /api/v1/mindmap/maps?subject={subject}&page={page}&limit={limit}
```

#### 节点管理接口
```javascript
// 创建节点
POST /api/v1/mindmap/nodes
{
  mapId: String,
  parentId: String,
  title: String,
  content: String,
  type: String,
  importance: Number,
  position: Object,
  style: Object
}

// 更新节点
PUT /api/v1/mindmap/nodes/{id}

// 删除节点
DELETE /api/v1/mindmap/nodes/{id}

// 批量操作节点
POST /api/v1/mindmap/nodes/batch
{
  action: String,  // create|update|delete
  nodes: Array
}
```

#### 任务关联接口
```javascript
// 从节点创建任务
POST /api/v1/mindmap/nodes/{id}/tasks
{
  taskData: Object,        // 任务数据
  options: {
    checkLoadBalance: Boolean,  // 是否检查负载均衡
    autoSchedule: Boolean       // 是否自动调度
  }
}

// 批量创建任务
POST /api/v1/mindmap/nodes/tasks/batch
{
  nodeIds: Array,          // 节点ID列表
  options: Object          // 创建选项
}

// 获取节点关联任务
GET /api/v1/mindmap/nodes/{id}/tasks

// 更新节点学习状态
PUT /api/v1/mindmap/nodes/{id}/status
{
  learningStatus: String,
  masteryLevel: Number,
  studyTime: Number
}
```

### 事件接口

#### 发出的事件
- **MINDMAP_NODE_TASK_CREATED**：从节点创建任务事件
- **MINDMAP_BATCH_TASKS_CREATED**：批量创建任务事件
- **MINDMAP_NODE_STATUS_CHANGED**：节点状态变更事件
- **MINDMAP_STRUCTURE_UPDATED**：导图结构更新事件

#### 监听的事件
- **TASK_STATUS_CHANGED**：任务状态变更事件
- **TASK_COMPLETED**：任务完成事件
- **LEARNING_EFFICIENCY_UPDATED**：学习效率更新事件
- **LOAD_BALANCE_WARNING**：负载预警事件

## 用户体验设计

### 操作流程设计

#### 新建思维导图流程
1. 选择导图模板或空白创建
2. 设置导图基本信息（标题、学科）
3. 创建中心节点
4. 逐步添加子节点和连接
5. 调整布局和样式
6. 保存和分享

#### 任务关联流程
1. 在导图中选择目标节点
2. 右键选择"创建学习任务"
3. 系统自动计算预估时间并显示
4. 检查负载均衡状况，显示预警（如有）
5. 用户确认任务内容和属性
6. 设置学习计划和提醒
7. 任务状态在导图中实时显示

#### 批量任务创建流程
1. 选择多个相关节点
2. 右键选择"批量创建任务"
3. 系统计算总体时间负荷
4. 显示负载均衡分析结果
5. 提供调度优化建议
6. 用户确认或调整计划
7. 批量创建并安排任务

### 操作体验优化

#### 简化操作
- **快速创建**：Tab键快速添加子节点，Enter键添加同级节点
- **直观编辑**：双击节点直接修改内容，拖拽调整位置
- **便捷管理**：右键菜单提供常用功能，支持撤销和重做
- **智能提示**：创建任务时显示时间预估和负载提醒

#### 视觉引导
- **状态区分**：不同颜色表示学习状态（未学习、学习中、已掌握）
- **重点标识**：重要知识点用特殊标记突出显示
- **关系展示**：连接线粗细和颜色反映知识点间的关联强度
- **负载提示**：任务创建时的负载状况可视化提示

## 模块协作

### 与任务管理模块协作
- **任务创建**：调用任务管理模块的创建接口
- **状态同步**：监听任务状态变更事件
- **数据关联**：维护节点与任务的关联关系
- **生命周期管理**：处理任务删除和修改

### 与时间管理模块协作
- **时间预估**：调用时间预估接口
- **负载检测**：调用负载均衡检测接口
- **调度优化**：获取任务调度建议
- **效率数据**：共享学习效率信息

### 与用户界面模块协作
- **可视化展示**：提供导图渲染数据
- **交互响应**：处理用户操作事件
- **状态反馈**：实时更新界面状态
- **错误处理**：提供友好的错误提示

---

**文档版本**：v2.0 重构版
**创建时间**：2025-01-31
**模块特点**：可视化知识管理、深度任务集成、智能学习引导
**依赖关系**：依赖任务管理和时间管理模块，为用户界面提供可视化服务
