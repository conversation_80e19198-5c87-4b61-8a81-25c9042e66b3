# 艾宾浩斯记忆曲线学习管理系统 - 术语表

## 📋 概述

本文档定义了艾宾浩斯记忆曲线学习管理系统项目中使用的所有术语和概念，确保团队成员和AI系统对概念理解的一致性。

## 🎯 核心概念定义

### [TERM-001] 艾宾浩斯记忆曲线
**术语ID**：TERM-001  
**英文名称**：Ebbinghaus Forgetting Curve  
**定义**：基于德国心理学家艾宾浩斯研究的遗忘规律，描述人类大脑对新事物遗忘的规律  
**系统应用**：用于计算最优复习时间间隔的算法基础  
**标准参数**：9个固定时间节点 [5分钟, 30分钟, 12小时, 1天, 3天, 1周, 2周, 1月, 2月]  
**相关术语**：[TERM-002] 复习计划, [TERM-003] 记忆强化

### [TERM-002] 复习计划
**术语ID**：TERM-002  
**英文名称**：Review Schedule  
**定义**：基于艾宾浩斯记忆曲线自动生成的复习时间安排  
**系统应用**：为每个学习任务自动生成9个复习时间点  
**生成规则**：任务创建时间 + 艾宾浩斯时间间隔  
**相关术语**：[TERM-001] 艾宾浩斯记忆曲线, [TERM-004] 学习任务

### [TERM-003] 记忆强化
**术语ID**：TERM-003  
**英文名称**：Memory Reinforcement  
**定义**：通过重复复习来加强记忆的过程  
**系统应用**：复习任务的执行和效果评估  
**评估指标**：复习完成率、记忆效果自评分  
**相关术语**：[TERM-002] 复习计划, [TERM-005] 复习任务

## 📚 任务管理术语

### [TERM-004] 学习任务
**术语ID**：TERM-004  
**英文名称**：Learning Task  
**定义**：用户创建的单个学习内容单元，包含学习材料和相关信息  
**数据结构**：标题、内容、学科、预估时间、优先级、难度等  
**状态流转**：待处理 → 进行中 → 已完成  
**相关术语**：[TERM-005] 复习任务, [TERM-006] 任务状态

### [TERM-005] 复习任务
**术语ID**：TERM-005  
**英文名称**：Review Task  
**定义**：基于学习任务和记忆曲线自动生成的复习活动  
**生成机制**：学习任务创建后自动生成9个复习任务  
**执行特点**：按时间节点提醒用户进行复习  
**相关术语**：[TERM-004] 学习任务, [TERM-002] 复习计划

### [TERM-006] 任务状态
**术语ID**：TERM-006  
**英文名称**：Task Status  
**定义**：任务在生命周期中的当前状态  
**状态枚举**：
- `pending`：待处理 - 任务已创建但未开始
- `active`：进行中 - 任务正在执行
- `completed`：已完成 - 任务已完成
- `cancelled`：已取消 - 任务被取消
**状态转换**：单向流转，不可逆转  
**相关术语**：[TERM-004] 学习任务, [TERM-005] 复习任务

## ⏰ 时间管理术语

### [TERM-007] 负载均衡
**术语ID**：TERM-007  
**英文名称**：Load Balancing  
**定义**：智能分配学习任务时间，避免某些时间段学习负载过重  
**计算方法**：每日任务总时长 = 新任务时长 + 复习任务时长  
**预警等级**：
- 轻度负载：10-30%
- 中度负载：30-60%  
- 重度负载：60%以上
**相关术语**：[TERM-008] 时间预估, [TERM-009] 调度优化

### [TERM-008] 时间预估
**术语ID**：TERM-008  
**英文名称**：Time Estimation  
**定义**：预测任务完成所需时间的算法  
**计算方式**：
- 新用户：基于用户输入的预估时间
- 老用户：基于个人学习效率历史数据
**影响因素**：学科类型、内容长度、个人效率系数  
**相关术语**：[TERM-009] 调度优化, [TERM-010] 学习效率

### [TERM-009] 调度优化
**术语ID**：TERM-009  
**英文名称**：Schedule Optimization  
**定义**：当检测到时间冲突或负载过重时，提供任务时间调整建议  
**触发条件**：负载超过预设阈值或多任务时间冲突  
**优化策略**：任务时间调整、优先级重排、任务分解  
**相关术语**：[TERM-007] 负载均衡, [TERM-008] 时间预估

### [TERM-010] 学习效率
**术语ID**：TERM-010  
**英文名称**：Learning Efficiency  
**定义**：个人学习能力的量化指标  
**计算公式**：学习效率 = 有效学习内容量 / 实际学习时间  
**应用场景**：时间预估算法的个性化参数  
**更新机制**：基于历史学习数据持续更新  
**相关术语**：[TERM-008] 时间预估, [TERM-011] 学习数据

## 🧠 思维导图术语

### [TERM-011] 思维导图
**术语ID**：TERM-011  
**英文名称**：Mind Map  
**定义**：用于可视化知识结构和概念关系的图形化工具  
**技术实现**：基于Cytoscape.js的图形渲染  
**核心功能**：节点创建、编辑、连接、样式设置  
**相关术语**：[TERM-012] 导图节点, [TERM-013] 任务关联

### [TERM-012] 导图节点
**术语ID**：TERM-012  
**英文名称**：Mind Map Node  
**定义**：思维导图中的基本元素，代表一个概念或知识点  
**属性信息**：标题、内容、样式、位置、连接关系  
**节点类型**：中心节点、子节点、叶子节点  
**相关术语**：[TERM-011] 思维导图, [TERM-013] 任务关联

### [TERM-013] 任务关联
**术语ID**：TERM-013  
**英文名称**：Task Association  
**定义**：思维导图节点与学习任务之间的关联关系  
**关联方式**：从导图节点直接创建学习任务  
**关联规则**：一个节点可关联多个任务，一个任务只能关联一个节点  
**同步机制**：任务状态变更在导图中实时显示  
**相关术语**：[TERM-012] 导图节点, [TERM-004] 学习任务

## 📊 数据管理术语

### [TERM-014] 学习数据
**术语ID**：TERM-014  
**英文名称**：Learning Data  
**定义**：用户学习过程中产生的各类数据记录  
**数据类型**：任务数据、复习记录、时间统计、效果评估  
**存储方式**：本地存储 + 云端同步  
**相关术语**：[TERM-015] 数据同步, [TERM-016] 学习分析

### [TERM-015] 数据同步
**术语ID**：TERM-015  
**英文名称**：Data Synchronization  
**定义**：本地数据与云端数据保持一致的机制  
**同步策略**：增量同步、冲突检测、版本控制  
**触发条件**：网络连接恢复、数据变更、定时同步  
**相关术语**：[TERM-014] 学习数据, [TERM-017] 离线模式

### [TERM-016] 学习分析
**术语ID**：TERM-016  
**英文名称**：Learning Analytics  
**定义**：基于学习数据进行的统计分析和洞察生成  
**分析维度**：时间分布、学科分布、完成率、效率趋势  
**输出形式**：图表、报告、个性化建议  
**相关术语**：[TERM-014] 学习数据, [TERM-010] 学习效率

### [TERM-017] 离线模式
**术语ID**：TERM-017  
**英文名称**：Offline Mode  
**定义**：网络断开时系统的工作模式  
**支持功能**：任务查看、复习执行、数据记录  
**限制功能**：数据同步、云端备份  
**恢复机制**：网络恢复后自动同步数据  
**相关术语**：[TERM-015] 数据同步, [TERM-014] 学习数据

## 👥 用户相关术语

### [TERM-018] 目标用户
**术语ID**：TERM-018  
**英文名称**：Target User  
**定义**：系统的主要服务对象  
**用户画像**：初中生（13-16岁）  
**使用特征**：学习任务繁重、接受新技术、注意力有限  
**设备偏好**：桌面端设备（台式机、笔记本）  
**相关术语**：[TERM-019] 用户体验, [TERM-020] 用户界面

### [TERM-019] 用户体验
**术语ID**：TERM-019  
**英文名称**：User Experience (UX)  
**定义**：用户在使用系统过程中的整体感受和体验  
**设计原则**：简洁性、一致性、可用性、响应性、容错性  
**评估指标**：易学性、效率性、记忆性、错误率、满意度  
**相关术语**：[TERM-020] 用户界面, [TERM-018] 目标用户

### [TERM-020] 用户界面
**术语ID**：TERM-020  
**英文名称**：User Interface (UI)  
**定义**：用户与系统交互的界面  
**设计特点**：桌面端优化、触摸友好、响应式布局  
**技术实现**：Vue 3 + Element Plus + Tailwind CSS  
**相关术语**：[TERM-019] 用户体验, [TERM-021] 交互设计

### [TERM-021] 交互设计
**术语ID**：TERM-021  
**英文名称**：Interaction Design  
**定义**：用户与系统交互方式的设计  
**交互方式**：鼠标操作、键盘操作、触摸手势、拖拽操作  
**设计要求**：即时反馈、状态提示、错误处理、操作引导  
**相关术语**：[TERM-020] 用户界面, [TERM-019] 用户体验

## 🔧 技术术语

### [TERM-022] 前端技术栈
**术语ID**：TERM-022  
**英文名称**：Frontend Technology Stack  
**定义**：前端开发使用的技术组合  
**核心技术**：Vue 3 + Vite + Element Plus + Tailwind CSS  
**辅助技术**：Pinia (状态管理)、Vue Router (路由)、Cytoscape.js (图形)  
**相关术语**：[TERM-023] 后端技术栈, [TERM-024] 技术架构

### [TERM-023] 后端技术栈
**术语ID**：TERM-023  
**英文名称**：Backend Technology Stack  
**定义**：后端开发使用的技术组合  
**核心技术**：Node.js + Express + MongoDB/MySQL  
**辅助技术**：PM2 (进程管理)、Redis (缓存)、Winston (日志)  
**相关术语**：[TERM-022] 前端技术栈, [TERM-024] 技术架构

### [TERM-024] 技术架构
**术语ID**：TERM-024  
**英文名称**：Technical Architecture  
**定义**：系统的整体技术结构设计  
**架构模式**：前后端分离、模块化设计、微服务架构  
**部署方案**：腾讯云 + Docker + CI/CD  
**相关术语**：[TERM-022] 前端技术栈, [TERM-023] 后端技术栈

## 📋 状态枚举定义

### 任务状态枚举
```typescript
enum TaskStatus {
  PENDING = 'pending',      // 待处理
  ACTIVE = 'active',        // 进行中  
  COMPLETED = 'completed',  // 已完成
  CANCELLED = 'cancelled'   // 已取消
}
```

### 复习状态枚举
```typescript
enum ReviewStatus {
  SCHEDULED = 'scheduled',    // 已安排
  IN_PROGRESS = 'in_progress', // 进行中
  COMPLETED = 'completed',    // 已完成
  SKIPPED = 'skipped'        // 已跳过
}
```

### 优先级枚举
```typescript
enum Priority {
  P0 = 'P0',  // 核心功能
  P1 = 'P1',  // 重要功能
  P2 = 'P2',  // 有用功能
  P3 = 'P3'   // 未来功能
}
```

### 学科枚举
```typescript
enum Subject {
  MATH = 'math',          // 数学
  CHINESE = 'chinese',    // 语文
  ENGLISH = 'english',    // 英语
  PHYSICS = 'physics',    // 物理
  CHEMISTRY = 'chemistry', // 化学
  BIOLOGY = 'biology',    // 生物
  HISTORY = 'history',    // 历史
  GEOGRAPHY = 'geography', // 地理
  POLITICS = 'politics'   // 政治
}
```

## 🔄 术语更新记录

| 版本 | 日期 | 变更内容 | 变更人 |
|------|------|----------|--------|
| v1.0 | 2025-01-31 | 初始术语表创建 | 系统分析师 |

---

**文档版本**：v1.0  
**创建时间**：2025-01-31  
**维护团队**：项目文档团队  
**适用范围**：所有项目文档和代码
