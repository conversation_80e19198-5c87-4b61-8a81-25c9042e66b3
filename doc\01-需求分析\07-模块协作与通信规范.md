# 07-模块协作与通信规范

## 规范概述

### 目标
建立艾宾浩斯记忆曲线学习管理系统各模块间的标准化协作机制，确保模块间依赖关系简单、维护成本低、扩展性强。

### 设计原则
1. **松耦合**：模块间通过标准接口通信，避免直接依赖
2. **高内聚**：每个模块职责明确，内部逻辑完整
3. **标准化**：统一的数据格式、接口规范、事件机制
4. **可维护**：清晰的模块边界，便于问题定位和修复
5. **可扩展**：支持新模块的动态接入和功能扩展

## 模块架构设计

### 模块定义与职责

#### 1. 任务管理核心模块 (TaskManager)
- **职责**：任务CRUD、艾宾浩斯曲线计算、提醒机制
- **优先级**：核心模块，其他模块的基础依赖
- **独立性**：完全独立，不依赖其他业务模块

#### 2. 智能时间管理模块 (TimeManager)
- **职责**：时间预估、负载计算、调度优化、学习效率分析
- **优先级**：重要模块，提供智能化服务
- **依赖关系**：依赖TaskManager的任务数据

#### 3. 思维导图模块 (MindMapManager)
- **职责**：知识结构可视化、导图编辑、任务关联
- **优先级**：扩展模块，增强用户体验
- **依赖关系**：依赖TaskManager和TimeManager的服务

#### 4. 用户界面模块 (UIManager)
- **职责**：用户交互、界面展示、操作引导
- **优先级**：表现层模块
- **依赖关系**：调用所有功能模块的服务

#### 5. 数据存储与同步模块 (DataManager)
- **职责**：数据持久化、离线支持、云端同步
- **优先级**：基础设施模块
- **依赖关系**：为所有模块提供数据服务

### 模块依赖关系图

```
用户界面模块 (表现层)
    ↓ (调用所有服务)
┌─────────────────────────────────┐
│ 思维导图模块                    │
│    ↓ (API调用)                  │
│ 智能时间管理模块                │
│    ↓ (API调用)                  │
│ 任务管理核心模块 (基础服务)     │
└─────────────────────────────────┘
    ↓ (数据访问)
数据存储与同步模块 (数据层)
```

**依赖关系说明：**
- 任务管理核心模块：基础服务，不依赖其他业务模块
- 智能时间管理模块：依赖任务管理模块的任务数据
- 思维导图模块：依赖任务管理和时间管理模块的服务
- 用户界面模块：调用所有功能模块的服务
- 数据存储模块：为所有模块提供数据服务

## 数据模型规范

### 统一数据实体

#### 任务数据模型 (Task)
```javascript
Task = {
  // 基础信息
  id: String,              // 任务唯一标识 (UUID)
  title: String,           // 任务标题 (必填, 1-100字符)
  content: String,         // 任务内容 (必填, 1-5000字符)
  subject: String,         // 学科分类
  
  // 时间信息
  estimatedTime: Number,   // 预估时间(分钟, 1-300)
  actualTime: Number,      // 实际时间(分钟, 0-600)
  createdAt: Date,         // 创建时间
  scheduledAt: Date,       // 计划执行时间
  completedAt: Date,       // 完成时间
  
  // 状态信息
  status: String,          // 状态: pending|active|completed|cancelled
  priority: Number,        // 优先级 (1-5, 5最高)
  difficulty: Number,      // 难度级别 (1-5, 5最难)
  
  // 来源信息
  source: String,          // 来源: manual|mindmap|import
  sourceId: String,        // 来源关联ID
  
  // 记忆曲线信息
  reviewSchedule: Array,   // 复习计划
  currentReviewIndex: Number, // 当前复习节点索引
  
  // 扩展信息
  tags: Array,            // 标签列表
  notes: String,          // 备注信息
  attachments: Array      // 附件列表
}
```

#### 学习效率数据模型 (LearningEfficiency)
```javascript
LearningEfficiency = {
  userId: String,          // 用户标识
  subject: String,         // 学科分类
  averageSpeed: Number,    // 平均学习速度 (字/分钟)
  validDataCount: Number,  // 有效数据条数
  totalStudyTime: Number,  // 总学习时间 (分钟)
  totalContentLength: Number, // 总内容长度 (字符数)
  lastUpdated: Date,       // 最后更新时间
  dataPoints: Array        // 历史数据点
}
```

#### 思维导图节点模型 (MindMapNode)
```javascript
MindMapNode = {
  id: String,              // 节点唯一标识
  mapId: String,           // 所属导图ID
  parentId: String,        // 父节点ID
  title: String,           // 节点标题
  content: String,         // 节点内容
  type: String,            // 节点类型
  importance: Number,      // 重要程度 (1-5)
  
  // 位置和样式
  position: {x: Number, y: Number},
  style: Object,
  
  // 学习相关
  taskIds: Array,          // 关联任务ID列表
  learningStatus: String,  // 学习状态
  masteryLevel: Number,    // 掌握程度 (0-100)
  studyTime: Number,       // 累计学习时间
  reviewCount: Number,     // 复习次数
  
  // 时间信息
  createdAt: Date,
  updatedAt: Date
}
```

### 数据验证规范

#### 必填字段验证
- 所有标记为"必填"的字段必须有值
- 字符串字段不能为空字符串
- 数值字段必须在指定范围内

#### 数据类型验证
- 严格按照定义的数据类型进行验证
- 日期字段必须是有效的ISO格式
- 枚举字段必须是预定义的值之一

#### 业务逻辑验证
- 完成时间不能早于创建时间
- 实际时间不能为负数
- 复习计划的日期必须按时间顺序排列

## 接口规范

### API接口标准

#### 接口命名规范
```
GET    /api/v1/{module}/{resource}           # 获取资源列表
GET    /api/v1/{module}/{resource}/{id}      # 获取单个资源
POST   /api/v1/{module}/{resource}           # 创建资源
PUT    /api/v1/{module}/{resource}/{id}      # 更新资源
DELETE /api/v1/{module}/{resource}/{id}      # 删除资源
```

#### 请求格式规范
```javascript
// 请求头
Headers = {
  'Content-Type': 'application/json',
  'Accept': 'application/json',
  'X-Module-Source': 'module_name',    // 调用方模块标识
  'X-Request-ID': 'uuid'               // 请求追踪ID
}

// 请求体 (POST/PUT)
RequestBody = {
  data: Object,        // 业务数据
  options: {           // 可选参数
    validate: Boolean, // 是否进行数据验证
    notify: Boolean    // 是否发送事件通知
  }
}
```

#### 响应格式规范
```javascript
// 成功响应
SuccessResponse = {
  success: true,
  data: Object|Array,     // 返回数据
  message: String,        // 成功消息
  code: 200,             // HTTP状态码
  timestamp: Date,       // 响应时间
  requestId: String      // 请求追踪ID
}

// 错误响应
ErrorResponse = {
  success: false,
  error: {
    code: Number,        // 错误码
    message: String,     // 错误消息
    details: Object      // 错误详情
  },
  timestamp: Date,
  requestId: String
}
```

### 模块间服务接口

#### TaskManager 提供的接口
```javascript
// 任务管理接口
POST   /api/v1/task/tasks                    # 创建任务
GET    /api/v1/task/tasks/{id}               # 获取任务详情
PUT    /api/v1/task/tasks/{id}               # 更新任务
DELETE /api/v1/task/tasks/{id}               # 删除任务
GET    /api/v1/task/tasks                    # 获取任务列表

// 复习计划接口
GET    /api/v1/task/schedule/{date}          # 获取指定日期的复习计划
POST   /api/v1/task/schedule/batch           # 批量创建复习计划
PUT    /api/v1/task/schedule/{taskId}        # 更新复习进度
```

#### TimeManager 提供的接口
```javascript
// 时间预估接口
POST   /api/v1/time/estimate                 # 计算任务预估时间
POST   /api/v1/time/estimate/batch           # 批量计算预估时间

// 负载均衡接口
GET    /api/v1/time/load/{date}              # 获取指定日期负载
POST   /api/v1/time/load/check               # 检查负载状况
POST   /api/v1/time/load/optimize            # 获取优化建议

// 学习效率接口
GET    /api/v1/time/efficiency/{subject}     # 获取学科学习效率
POST   /api/v1/time/efficiency/update        # 更新学习效率数据
```

#### MindMapManager 提供的接口
```javascript
// 思维导图接口
POST   /api/v1/mindmap/maps                  # 创建思维导图
GET    /api/v1/mindmap/maps/{id}             # 获取思维导图
PUT    /api/v1/mindmap/maps/{id}             # 更新思维导图

// 节点管理接口
POST   /api/v1/mindmap/nodes                 # 创建节点
PUT    /api/v1/mindmap/nodes/{id}            # 更新节点
DELETE /api/v1/mindmap/nodes/{id}            # 删除节点

// 任务关联接口
POST   /api/v1/mindmap/nodes/{id}/tasks      # 从节点创建任务
GET    /api/v1/mindmap/nodes/{id}/tasks      # 获取节点关联任务
```

## 事件通信规范

### 事件系统设计

#### 事件总线架构
```javascript
EventBus = {
  subscribe(eventType, callback),    // 订阅事件
  unsubscribe(eventType, callback),  // 取消订阅
  publish(eventType, data),          // 发布事件
  once(eventType, callback)          // 一次性订阅
}
```

#### 事件数据格式
```javascript
Event = {
  type: String,           // 事件类型 (必填)
  source: String,         // 事件来源模块 (必填)
  target: String,         // 目标模块 (可空, 空表示广播)
  data: Object,           // 事件数据 (必填)
  timestamp: Date,        // 事件时间 (自动生成)
  id: String,            // 事件ID (自动生成)
  version: String         // 事件版本 (默认v1.0)
}
```

### 标准事件定义

#### 任务相关事件
```javascript
// 任务生命周期事件
TASK_CREATED = {
  type: 'TASK_CREATED',
  source: 'TaskManager',
  data: {
    task: Task,           // 完整任务对象
    source: String        // 创建来源
  }
}

TASK_COMPLETED = {
  type: 'TASK_COMPLETED',
  source: 'TaskManager',
  data: {
    taskId: String,       // 任务ID
    completedAt: Date,    // 完成时间
    actualTime: Number    // 实际用时
  }
}

TASK_UPDATED = {
  type: 'TASK_UPDATED',
  source: 'TaskManager',
  data: {
    taskId: String,       // 任务ID
    changes: Object,      // 变更字段
    oldValues: Object     // 原始值
  }
}

TASK_DELETED = {
  type: 'TASK_DELETED',
  source: 'TaskManager',
  data: {
    taskId: String,       // 被删除的任务ID
    sourceId: String      // 来源关联ID
  }
}
```

#### 时间管理相关事件
```javascript
// 负载均衡事件
LOAD_BALANCE_WARNING = {
  type: 'LOAD_BALANCE_WARNING',
  source: 'TimeManager',
  data: {
    date: Date,           // 超载日期
    overloadLevel: String, // 超载等级
    currentLoad: Number,   // 当前负载(分钟)
    maxLoad: Number,      // 最大负载(分钟)
    suggestions: Array    // 优化建议
  }
}

SCHEDULE_OPTIMIZED = {
  type: 'SCHEDULE_OPTIMIZED',
  source: 'TimeManager',
  data: {
    originalDate: Date,   // 原始日期
    adjustments: Array,   // 调整方案
    reason: String        // 调整原因
  }
}

LEARNING_EFFICIENCY_UPDATED = {
  type: 'LEARNING_EFFICIENCY_UPDATED',
  source: 'TimeManager',
  data: {
    subject: String,      // 学科
    oldEfficiency: Number, // 原效率
    newEfficiency: Number, // 新效率
    dataCount: Number     // 数据量
  }
}
```

#### 思维导图相关事件
```javascript
// 思维导图事件
MINDMAP_NODE_TASK_CREATED = {
  type: 'MINDMAP_NODE_TASK_CREATED',
  source: 'MindMapManager',
  data: {
    nodeId: String,       // 节点ID
    taskIds: Array,       // 创建的任务ID列表
    batchSize: Number     // 批量大小
  }
}

MINDMAP_NODE_STATUS_CHANGED = {
  type: 'MINDMAP_NODE_STATUS_CHANGED',
  source: 'MindMapManager',
  data: {
    nodeId: String,       // 节点ID
    oldStatus: String,    // 原状态
    newStatus: String,    // 新状态
    masteryLevel: Number  // 掌握程度
  }
}
```

### 事件处理规范

#### 事件订阅规范
```javascript
// 模块初始化时订阅相关事件
class ModuleManager {
  constructor() {
    this.subscribeEvents();
  }
  
  subscribeEvents() {
    EventBus.subscribe('TASK_COMPLETED', this.onTaskCompleted.bind(this));
    EventBus.subscribe('LOAD_BALANCE_WARNING', this.onLoadWarning.bind(this));
  }
  
  onTaskCompleted(event) {
    // 处理任务完成事件
    const { taskId, completedAt, actualTime } = event.data;
    this.updateNodeStatus(taskId, 'completed');
  }
}
```

#### 事件发布规范
```javascript
// 发布事件时的标准流程
publishEvent(type, data) {
  try {
    // 数据验证
    this.validateEventData(type, data);
    
    // 发布事件
    EventBus.publish(type, {
      source: this.moduleName,
      data: data,
      timestamp: new Date(),
      id: this.generateEventId()
    });
    
    // 记录日志
    this.logEvent(type, data);
  } catch (error) {
    this.handleEventError(error);
  }
}
```

## 错误处理规范

### 错误分类

#### 系统错误 (1000-1999)
- 1001: 模块初始化失败
- 1002: 数据库连接失败
- 1003: 网络通信失败
- 1004: 配置文件错误

#### 业务错误 (2000-2999)
- 2001: 数据验证失败
- 2002: 业务规则冲突
- 2003: 权限不足
- 2004: 资源不存在

#### 接口错误 (3000-3999)
- 3001: 接口参数错误
- 3002: 接口调用超时
- 3003: 接口返回格式错误
- 3004: 接口版本不兼容

### 错误处理策略

#### 错误捕获
```javascript
// 统一错误处理中间件
function errorHandler(error, req, res, next) {
  const errorInfo = {
    code: error.code || 5000,
    message: error.message,
    details: error.details || {},
    timestamp: new Date(),
    requestId: req.headers['x-request-id']
  };
  
  // 记录错误日志
  logger.error('API Error:', errorInfo);
  
  // 返回错误响应
  res.status(error.httpStatus || 500).json({
    success: false,
    error: errorInfo
  });
}
```

#### 错误恢复
```javascript
// 模块间调用的错误恢复
async function callModuleAPI(url, data, options = {}) {
  const maxRetries = options.maxRetries || 3;
  const retryDelay = options.retryDelay || 1000;
  
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await httpClient.post(url, data);
    } catch (error) {
      if (i === maxRetries - 1) {
        // 最后一次重试失败，使用降级策略
        return this.fallbackStrategy(error);
      }
      
      // 等待后重试
      await this.delay(retryDelay * Math.pow(2, i));
    }
  }
}
```

## 配置管理规范

### 模块配置结构
```javascript
module.exports = {
  // 模块基本信息
  module: {
    name: 'TimeManager',
    version: '2.0.0',
    description: '智能时间管理模块'
  },
  
  // 依赖模块
  dependencies: {
    TaskManager: '^2.0.0',
    EventBus: '^1.0.0'
  },
  
  // API配置
  api: {
    baseUrl: '/api/v1/time',
    timeout: 5000,
    retries: 3
  },
  
  // 事件配置
  events: {
    subscribe: [
      'TASK_CREATED',
      'TASK_COMPLETED',
      'TASK_UPDATED'
    ],
    publish: [
      'LOAD_BALANCE_WARNING',
      'SCHEDULE_OPTIMIZED',
      'LEARNING_EFFICIENCY_UPDATED'
    ]
  },
  
  // 业务配置
  business: {
    maxDailyLoadMinutes: 120,
    overloadThresholds: {
      light: 0.1,
      medium: 0.3,
      heavy: 0.6
    }
  }
};
```

## 部署和监控规范

### 模块部署
- 每个模块可以独立部署和更新
- 使用SSH直连服务器部署（移除Docker依赖）
- 支持热更新和版本回滚
- 基于PM2进程管理实现零停机部署

### 监控指标
- **性能指标**：API响应时间、事件处理延迟、数据库查询时间
- **业务指标**：任务创建成功率、负载均衡准确率、用户操作成功率
- **系统指标**：内存和CPU使用率、模块间调用成功率

### 告警规则
```javascript
const alertRules = {
  // API响应时间超过5秒
  'api_response_time': {
    threshold: 5000,
    severity: 'warning'
  },
  
  // 错误率超过5%
  'error_rate': {
    threshold: 0.05,
    severity: 'critical'
  },
  
  // 内存使用率超过80%
  'memory_usage': {
    threshold: 0.8,
    severity: 'warning'
  }
};
```

---

**规范版本**：v2.0 重构版
**制定时间**：2025-01-31
**适用范围**：艾宾浩斯记忆曲线学习管理系统所有模块
**维护责任**：系统架构师和各模块负责人
