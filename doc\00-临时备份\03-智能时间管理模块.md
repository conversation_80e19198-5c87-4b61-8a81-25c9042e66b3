# 03-智能时间管理模块

## 模块概述

### 模块名称
智能时间管理模块 (TimeManager)

### 功能定位
提供智能化的时间管理服务，解决艾宾浩斯记忆曲线学习中的时间分配和任务负载均衡问题，通过智能计算和预警机制，帮助用户合理规划学习时间。

### 设计目标
- 智能预估任务完成时间
- 实时监控学习负载状况
- 提供负载均衡优化建议
- 分析个人学习效率趋势
- 预防任务过度集中问题

### 核心价值
- 科学的时间预估算法
- 可视化的负载监控
- 智能的调度优化
- 个性化的效率分析

## 核心问题分析

### 问题本质
- **X轴（时间）**：艾宾浩斯记忆曲线的9个时间节点
- **Y轴（任务量）**：用户创建的多个学习任务
- **复杂性**：X轴和Y轴的叠加效应导致某些日期任务量激增
- **用户痛点**：无法预见未来的学习负荷，难以合理分配时间
- **新增挑战**：思维导图批量任务创建对负载均衡的冲击

### 关键影响因子

#### A. 时间管理因子
1. **预估时间因子**：用户主观判断任务完成时长
2. **实际时间因子**：系统客观记录的执行时间
3. **学习速度因子**：基于历史数据的个人化学习效率
4. **异常过滤因子**：识别和排除不正常的时间数据

#### B. 负载计算因子
1. **时间节点因子**：9个固定的复习时间点
2. **任务分布因子**：任务在时间轴上的分布密度
3. **时长累计因子**：每日任务总时间的计算
4. **预算约束因子**：用户设定的每日学习时间上限

#### C. 智能调度因子
1. **超载检测因子**：基于阈值的负荷预警
2. **调整策略因子**：任务重新分配的算法逻辑
3. **决策选项因子**：用户面临超载时的选择机制
4. **效果保护因子**：确保调整不影响记忆效果

## 核心功能设计

### 1. 智能时间预估

#### 1.1 预估算法
- **新用户模式**：前10次任务使用用户预估时间
- **历史数据模式**：基于个人学习效率计算预期时间
- **学科差异化**：不同学科采用不同的效率系数
- **内容长度分析**：基于任务内容长度进行时间预估

#### 1.2 学习效率计算
```javascript
// 学习效率计算公式
学习效率 = Σ(有效任务字数) / Σ(有效学习时间)

// 有效数据筛选条件
有效数据 = {
  时间范围: 5分钟 ≤ 学习时间 ≤ 180分钟,
  速度范围: 平均速度 × 70% ≤ 学习速度 ≤ 平均速度 × 130%,
  状态过滤: 排除中断和异常状态的学习记录
}
```

#### 1.3 预估优化
- **自适应调整**：根据实际完成情况持续优化预测准确性
- **学科权重**：不同学科设置不同的时间权重系数
- **难度系数**：根据任务难度级别调整预估时间
- **个人化修正**：基于个人学习习惯进行预估修正

### 2. 负载均衡监控

#### 2.1 负载计算
```javascript
// 每日负荷计算公式
每日总负荷 = Σ(该日所有任务的预期时长)
预期时长 = 任务字数 / 个人学习效率

// 超载程度计算
超载程度 = (实际负荷 - 时间预算) / 时间预算 × 100%
```

#### 2.2 可视化展示
- **日历视图**：以日历形式展示每日任务负荷
- **柱状图表**：直观显示每日任务时长分布
- **热力图**：用颜色深浅表示负荷密度
- **趋势图**：显示未来30天的负荷变化趋势

#### 2.3 负荷分析
- **任务数量统计**：每日需要复习的任务总数
- **时间累计计算**：每日任务的总预期时长
- **负荷密度分析**：识别高峰期和低谷期
- **来源分析**：区分普通任务和思维导图任务的负荷贡献

### 3. 智能超载预警

#### 3.1 预警机制
- **实时检测**：用户创建新任务时立即计算影响
- **批量检测**：思维导图批量创建任务时的专门检测
- **提前预警**：提前3-7天预警即将到来的超载
- **分级提醒**：根据超载程度提供不同级别的提醒

#### 3.2 超载等级
```javascript
超载等级定义 = {
  轻度超载: "10%-30%，建议适当调整",
  中度超载: "30%-60%，需要重新安排",
  重度超载: "60%以上，必须优化调度"
}
```

#### 3.3 预警触发
- **阈值设置**：用户可自定义超载预警阈值
- **智能建议**：自动推荐最优的调整策略
- **影响评估**：显示超载对学习计划的影响
- **解决方案**：提供多种负载优化选项

### 4. 智能调度优化

#### 4.1 调度策略
**选项一：智能调整模式**
- 自动将部分任务分散到相邻日期
- 优先调整对记忆效果影响最小的任务
- 保持整体学习节奏的连续性
- 显示调整前后的负荷对比

**选项二：手动调整模式**
- 显示具体的超载任务列表
- 允许用户手动选择要调整的任务
- 提供多个可选的调整日期
- 实时显示调整后的负荷变化

**选项三：停止录入模式**
- 暂停新任务的创建
- 建议用户先完成现有任务
- 提供负荷缓解的时间预估
- 支持稍后继续录入

#### 4.2 调整算法
```javascript
// 调整优先级计算
调整优先级 = 复习次数权重 × 0.4 + 
           任务重要性权重 × 0.3 + 
           时间灵活性权重 × 0.3

// 可调整任务筛选条件
可调整任务 = {
  复习次数: >= 2,  // 已复习过的任务
  时间灵活性: > 0, // 可调整的任务
  调整范围: ±3天   // 限制调整范围
}
```

#### 4.3 效果保护
- **记忆间隔保护**：确保调整不破坏艾宾浩斯曲线的科学性
- **最小调整原则**：优先选择调整幅度最小的方案
- **效果评估**：跟踪调整后的学习效果，持续优化算法
- **用户反馈**：收集用户对调整结果的满意度

## 数据模型设计

### 学习效率数据模型
```javascript
LearningEfficiency = {
  userId: String,          // 用户标识
  subject: String,         // 学科分类
  averageSpeed: Number,    // 平均学习速度 (字/分钟)
  validDataCount: Number,  // 有效数据条数
  totalStudyTime: Number,  // 总学习时间 (分钟)
  totalContentLength: Number, // 总内容长度 (字符数)
  lastUpdated: Date,       // 最后更新时间
  dataPoints: Array        // 历史数据点
}
```

### 负载统计模型
```javascript
LoadStatistics = {
  date: Date,              // 统计日期
  totalTasks: Number,      // 任务总数
  totalTime: Number,       // 总时间 (分钟)
  tasksBySource: {         // 按来源分类
    manual: Number,        // 手动创建
    mindmap: Number        // 思维导图创建
  },
  tasksBySubject: Object,  // 按学科分类
  overloadLevel: String,   // 超载等级
  suggestions: Array       // 优化建议
}
```

## 接口设计

### API接口

#### 时间预估接口
```javascript
// 计算任务预估时间
POST /api/v1/time/estimate
{
  content: String,         // 任务内容
  subject: String,         // 学科分类
  difficulty: Number       // 难度级别
}

// 批量计算预估时间
POST /api/v1/time/estimate/batch
{
  tasks: Array            // 任务列表
}
```

#### 负载均衡接口
```javascript
// 获取指定日期负载
GET /api/v1/time/load/{date}

// 检查负载状况
POST /api/v1/time/load/check
{
  tasks: Array,           // 任务列表
  targetDate: Date        // 目标日期
}

// 获取优化建议
POST /api/v1/time/load/optimize
{
  overloadDate: Date,     // 超载日期
  tasks: Array,           // 相关任务
  userPreferences: Object // 用户偏好
}
```

#### 学习效率接口
```javascript
// 获取学科学习效率
GET /api/v1/time/efficiency/{subject}

// 更新学习效率数据
POST /api/v1/time/efficiency/update
{
  taskId: String,         // 任务ID
  actualTime: Number,     // 实际用时
  contentLength: Number   // 内容长度
}
```

### 事件接口

#### 发出的事件
- **LOAD_BALANCE_WARNING**：负载预警事件
- **SCHEDULE_OPTIMIZED**：调度优化事件
- **LEARNING_EFFICIENCY_UPDATED**：学习效率更新事件
- **TIME_ESTIMATE_CALCULATED**：时间预估完成事件

#### 监听的事件
- **TASK_CREATED**：任务创建事件
- **TASK_COMPLETED**：任务完成事件
- **MINDMAP_BATCH_TASKS_CREATED**：批量任务创建事件

## 性能优化

### 算法优化
- **缓存机制**：缓存学习效率计算结果
- **增量更新**：只计算变化部分的负载
- **预计算**：提前计算未来负载趋势
- **采样计算**：大数据量时使用采样策略

### 数据优化
- **索引优化**：按日期和用户建立索引
- **数据分区**：按时间范围分区存储
- **缓存策略**：热点数据内存缓存
- **批量处理**：批量更新学习效率数据

### 响应优化
- **异步处理**：耗时计算采用异步处理
- **分页加载**：大量数据分页展示
- **懒加载**：按需加载详细数据
- **压缩传输**：API响应数据压缩

## 模块协作

### 依赖关系
- **依赖任务管理模块**：获取任务数据和状态信息
- **服务思维导图模块**：提供时间预估和负载检测服务
- **服务用户界面模块**：提供可视化数据和交互接口

### 数据交互
- **接收任务数据**：从任务管理模块获取任务信息
- **提供预估服务**：为其他模块提供时间预估
- **发送预警信息**：向界面模块发送负载预警
- **同步效率数据**：与其他模块共享学习效率数据

---

**文档版本**：v2.0 重构版
**创建时间**：2025-01-31
**模块特点**：智能算法驱动、可视化监控、个性化优化
**依赖关系**：依赖任务管理模块，为思维导图和界面模块提供服务
